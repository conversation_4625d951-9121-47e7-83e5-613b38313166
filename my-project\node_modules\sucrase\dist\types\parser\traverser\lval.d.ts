import { TokenType } from "../tokenizer/types";
export declare function parseSpread(): void;
export declare function parseRest(isBlockScope: boolean): void;
export declare function parseBindingIdentifier(isBlockScope: boolean): void;
export declare function parseImportedIdentifier(): void;
export declare function markPriorBindingIdentifier(isBlockScope: boolean): void;
export declare function parseBinding<PERSON>tom(isBlockScope: boolean): void;
export declare function parseBindingList(close: TokenType, isBlockScope: boolean, allowEmpty?: boolean, allowModifiers?: boolean, contextId?: number): void;
export declare function parseMaybeDefault(isBlockScope: boolean, leftAlreadyParsed?: boolean): void;
