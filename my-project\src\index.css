@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Open Sans', sans-serif;
    margin: 0;
    padding: 0;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-[#2E7D32] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#1B5E20] transition-colors duration-300;
  }

  .btn-secondary {
    @apply bg-[#FFC107] text-[#2E7D32] px-6 py-3 rounded-lg font-semibold hover:bg-[#FFB300] transition-colors duration-300;
  }

  .btn-outline {
    @apply border-2 border-[#2E7D32] text-[#2E7D32] px-6 py-3 rounded-lg font-semibold hover:bg-[#2E7D32] hover:text-white transition-all duration-300;
  }

  .section-padding {
    @apply py-16 px-4 sm:px-6 lg:px-8;
  }

  .container-custom {
    @apply max-w-7xl mx-auto;
  }

  .fade-in {
    animation: fadeIn 0.8s ease-in-out;
  }

  .slide-up {
    animation: slideUp 0.8s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
